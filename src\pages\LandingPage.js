import React from "react";
import "./LandingPage.css";
import Navbar from "../layouts/Navbar";
import Hero from "../layouts/Hero";
import About from "../layouts/About";
import Projects from "../layouts/Projects";
import Experience from "../layouts/Experience";
import Contact from "../layouts/Contact";
import Footer from "../layouts/Footer";

const LandingPage = () => {
  // Mock data
  const personalInfo = {
    name: "<PERSON>",
    title: "Full Stack Developer",
    tagline: "Building digital experiences that make a difference",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    profileImage:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
  };

  const skills = [
    "JavaScript",
    "React",
    "Node.js",
    "Python",
    "TypeScript",
    "MongoDB",
    "PostgreSQL",
    "AWS",
    "Docker",
    "Git",
  ];

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      description:
        "A full-stack e-commerce solution with React, Node.js, and MongoDB",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      image:
        "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 2,
      title: "Task Management App",
      description:
        "A collaborative task management application with real-time updates",
      technologies: ["React", "Socket.io", "Express", "PostgreSQL"],
      image:
        "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      id: 3,
      title: "Weather Dashboard",
      description:
        "A responsive weather dashboard with location-based forecasts",
      technologies: ["JavaScript", "CSS3", "Weather API", "Chart.js"],
      image:
        "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=400&h=250&fit=crop",
      liveUrl: "#",
      githubUrl: "#",
    },
  ];

  const experience = [
    {
      id: 1,
      company: "TechCorp Solutions",
      position: "Senior Full Stack Developer",
      duration: "2022 - Present",
      description: "Lead development of web applications serving 100k+ users",
    },
    {
      id: 2,
      company: "StartupXYZ",
      position: "Frontend Developer",
      duration: "2020 - 2022",
      description:
        "Built responsive web applications using React and modern JavaScript",
    },
    {
      id: 3,
      company: "Digital Agency",
      position: "Junior Developer",
      duration: "2019 - 2020",
      description:
        "Developed client websites and learned full-stack development",
    },
  ];

  return (
    <div className="landing-page">
      <Navbar personalInfo={personalInfo} />

      <Hero personalInfo={personalInfo} />

      <About skills={skills} />

      <Projects projects={projects} />

      <Experience experience={experience} />

      <Contact personalInfo={personalInfo} />

      <Footer personalInfo={personalInfo} />
    </div>
  );
};

export default LandingPage;
